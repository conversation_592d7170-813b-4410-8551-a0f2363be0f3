#!/usr/bin/env python3
"""
Clang-based C++ parser implementation for more accurate parsing.
"""

import os
from concurrent.futures import Thread<PERSON>oolExecutor

try:
    import clang.cindex as cindex

    CLANG_AVAILABLE = True
except ImportError:
    CLANG_AVAILABLE = False

from tqdm import tqdm

from cpp_inheritance_analyzer.models.class_info import ClassInfo
from cpp_inheritance_analyzer.parsers.base_parser import BaseParser


class ClangCppParser(BaseParser):
    """C++ source code parser using Clang/LLVM for accurate class information extraction."""

    def __init__(
        self,
        compilation_flags: list[str] = None,
        use_cache: bool = True,
        cache_dir: str | None = None,
        precompiled_header: bool = False,
    ):
        """
        Initialize the Clang C++ parser.

        Args:
            compilation_flags: Custom compilation flags for Clang
            use_cache: Whether to use persistent caching
            cache_dir: Directory to store cache files
            precompiled_header: Whether to use precompiled headers for faster parsing
        """
        super().__init__(use_cache=use_cache, cache_dir=cache_dir)
        if not CLANG_AVAILABLE:
            raise ImportError(
                "Clang Python bindings not available. Install with: pip install clang"
            )

        # Initialize Clang index with optimized settings
        # Enable global caching for better performance with multiple files
        self.index = cindex.Index.create(excludeDecls=True)

        # Track performance metrics
        self.parse_times = []
        self.precompiled_header = precompiled_header

        # Find system include paths automatically
        system_includes = self._detect_system_includes()

        # Default compilation flags for parsing with optimized settings
        self.compilation_flags = compilation_flags or [
            "-x",
            "c++",  # Force C++ language
            "-std=c++17",  # Use C++17 standard
            "-fno-delayed-template-parsing",  # Speed up template parsing
            "-Wno-unknown-warning-option",  # Suppress warnings
            "-ferror-limit=100",  # Limit error reporting
            "-fparse-all-comments",  # Parse comments for documentation
        ]

        # Add system include paths
        self.compilation_flags.extend(system_includes)

    def _detect_system_includes(self) -> list[str]:
        """Detect system include paths automatically."""
        system_includes = []

        # Common system include directories
        common_dirs = [
            "/usr/include",
            "/usr/local/include",
            "/usr/include/c++/*/include",
            "/usr/lib/gcc/*/*/include",
        ]

        # Add directories that actually exist
        for include_dir in common_dirs:
            if "*" in include_dir:
                # Handle wildcard paths
                import glob

                for path in glob.glob(include_dir):
                    if os.path.isdir(path):
                        system_includes.append(f"-I{path}")
            elif os.path.isdir(include_dir):
                system_includes.append(f"-I{include_dir}")

        return system_includes

    def _get_access_specifier(self, access_specifier) -> str:
        """Convert Clang access specifier to string."""
        if access_specifier == cindex.AccessSpecifier.PUBLIC:
            return "public"
        elif access_specifier == cindex.AccessSpecifier.PROTECTED:
            return "protected"
        elif access_specifier == cindex.AccessSpecifier.PRIVATE:
            return "private"
        else:
            return "private"  # Default

    def _extract_template_params(self, node) -> list[str]:
        """Extract template parameters from a class template."""
        template_params = []

        # Check if this is a class template
        if node.kind == cindex.CursorKind.CLASS_TEMPLATE:
            # Iterate through children to find template parameters
            for child in node.get_children():
                if child.kind == cindex.CursorKind.TEMPLATE_TYPE_PARAMETER:
                    template_params.append(child.spelling or "T")
                elif child.kind == cindex.CursorKind.TEMPLATE_NON_TYPE_PARAMETER:
                    template_params.append(child.spelling or "N")

        return template_params

    def _get_namespace(self, node) -> str:
        """Get the full namespace of a node."""
        namespaces = []
        parent = node.semantic_parent

        while parent and parent.kind != cindex.CursorKind.TRANSLATION_UNIT:
            if parent.kind == cindex.CursorKind.NAMESPACE:
                namespaces.insert(0, parent.spelling)
            parent = parent.semantic_parent

        return "::".join(namespaces) if namespaces else ""

    def _extract_class_info(self, node, file_path: str) -> ClassInfo | None:
        """Extract class information from a Clang cursor."""
        # Skip forward declarations without a body
        if not any(
            child.kind == cindex.CursorKind.CXX_BASE_SPECIFIER
            for child in node.get_children()
        ):
            has_body = False
            for child in node.get_children():
                if child.kind in [
                    cindex.CursorKind.FIELD_DECL,
                    cindex.CursorKind.CXX_METHOD,
                ]:
                    has_body = True
                    break
            if not has_body:
                return None

        # Get class name
        class_name = node.spelling
        if not class_name:
            return None

        # Get line number
        line_number = node.location.line

        # Determine if it's a struct or class
        is_struct = node.kind == cindex.CursorKind.STRUCT_DECL

        # Get namespace
        namespace = self._get_namespace(node)

        # Get template parameters
        template_params = self._extract_template_params(node)

        # Extract base classes and access specifiers
        base_classes = []
        access_specifiers = []

        for child in node.get_children():
            if child.kind == cindex.CursorKind.CXX_BASE_SPECIFIER:
                base_class = child.get_definition()
                if base_class:
                    base_name = base_class.spelling
                    base_namespace = self._get_namespace(base_class)
                    full_base_name = (
                        f"{base_namespace}::{base_name}"
                        if base_namespace
                        else base_name
                    )

                    base_classes.append(full_base_name)
                    access_specifiers.append(
                        self._get_access_specifier(child.access_specifier)
                    )

        return ClassInfo(
            name=class_name,
            file_path=file_path,
            line_number=line_number,
            base_classes=base_classes,
            access_specifiers=access_specifiers,
            is_struct=is_struct,
            namespace=namespace,
            template_params=template_params,
        )

    def parse_file(self, file_path: str) -> list[ClassInfo]:
        """Parse a single C++ file using Clang and extract class information."""
        # Check in-memory cache first
        if file_path in self.file_cache:
            return self.file_cache[file_path]

        # Try to load from persistent cache
        cache_hit, cached_classes = self._load_from_cache(file_path)
        if cache_hit:
            self.file_cache[file_path] = cached_classes
            return cached_classes

        try:
            # Parse the file with Clang - optimize parsing options
            # PARSE_INCOMPLETE: Parse even if there are errors
            # PARSE_SKIP_FUNCTION_BODIES: Skip function bodies to speed up parsing
            # PARSE_CACHE_COMPLETION_RESULTS: Cache results for better performance
            translation_unit = self.index.parse(
                file_path,
                args=self.compilation_flags,
                options=cindex.TranslationUnit.PARSE_INCOMPLETE
                | cindex.TranslationUnit.PARSE_SKIP_FUNCTION_BODIES
                | cindex.TranslationUnit.PARSE_CACHE_COMPLETION_RESULTS,
            )

            if not translation_unit:
                self.logger.warning(f"Failed to parse {file_path} with Clang")
                return []

            # Extract class information
            classes = []

            # Use non-recursive traversal to avoid stack overflow
            # and improve performance for large files
            nodes_to_visit = [translation_unit.cursor]
            while nodes_to_visit:
                node = nodes_to_visit.pop()

                # Only process nodes from the current file
                if node.location.file and node.location.file.name == file_path:
                    if node.kind in [
                        cindex.CursorKind.CLASS_DECL,
                        cindex.CursorKind.STRUCT_DECL,
                        cindex.CursorKind.CLASS_TEMPLATE,
                    ]:
                        class_info = self._extract_class_info(node, file_path)
                        if class_info:
                            classes.append(class_info)

                # Add children to the stack in reverse order
                # so they are processed in the original order
                children = list(node.get_children())
                nodes_to_visit.extend(reversed(children))

            # Save to in-memory cache
            self.file_cache[file_path] = classes

            # Save to persistent cache
            self._save_to_cache(file_path, classes)

            return classes

        except Exception as e:
            self.logger.warning(f"Error parsing {file_path} with Clang: {e}")
            return []

    def parse_directory(
        self,
        directory: str,
        extensions: set[str] = None,
        max_workers: int = None,
        batch_size: int = 50,
    ) -> list[ClassInfo]:
        """
        Parse all C++ files in a directory tree using Clang.

        Args:
            directory: Root directory to search for C++ files
            extensions: Set of file extensions to consider
            max_workers: Maximum number of worker threads
            batch_size: Number of files to process in each batch for better load balancing
        """
        if extensions is None:
            extensions = {".h", ".hpp", ".hxx", ".cpp", ".cc", ".cxx", ".c++"}

        # Find all relevant files
        cpp_files = []
        for root, dirs, files in os.walk(directory):
            # Skip common build directories and hidden directories
            dirs[:] = [
                d
                for d in dirs
                if not d.startswith(".")
                and d
                not in {
                    "build",
                    "cmake-build-debug",
                    "cmake-build-release",
                    "__pycache__",
                    "node_modules",
                    "out",
                    "bin",
                    "obj",
                    "Debug",
                    "Release",
                    "x64",
                    "x86",
                }
            ]

            for file in files:
                if any(file.endswith(ext) for ext in extensions):
                    cpp_files.append(os.path.join(root, file))

        self.logger.info(f"Found {len(cpp_files)} C++ files to parse with Clang")

        # Optimize worker count based on system resources
        if max_workers is None:
            # Use more workers for I/O bound tasks
            max_workers = min(64, os.cpu_count() * 2 + 4)

        # Parse files in parallel with improved batching for better load balancing
        all_classes = []

        # Check for interrupt flag
        import sys

        interrupted = False
        if "cpp_inheritance_analyzer" in sys.modules:
            # Get interrupt flag from main module
            interrupted = getattr(
                sys.modules["cpp_inheritance_analyzer"], "interrupted", False
            )

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Create a progress bar for the parsing process
            with tqdm(
                total=len(cpp_files),
                desc="Parsing C++ files",
                unit="file",
                ncols=100,
                colour="green",
            ) as pbar:
                # Track submitted tasks
                futures_map = {}

                try:
                    # Process files in batches for better load balancing
                    for i in range(0, len(cpp_files), batch_size):
                        # Check if interrupted
                        if interrupted or getattr(
                            sys.modules.get("cpp_inheritance_analyzer", None),
                            "interrupted",
                            False,
                        ):
                            self.logger.info(
                                "Interrupt signal detected, stopping submission of new tasks"
                            )
                            break

                        batch = cpp_files[i : i + batch_size]
                        batch_futures = []

                        for file_path in batch:
                            future = executor.submit(
                                self._parse_file_with_progress, file_path, pbar
                            )
                            futures_map[future] = file_path
                            batch_futures.append(future)

                        # Collect completed results
                        for future in batch_futures:
                            try:
                                if not interrupted and not getattr(
                                    sys.modules.get("cpp_inheritance_analyzer", None),
                                    "interrupted",
                                    False,
                                ):
                                    file_classes = future.result()
                                    all_classes.extend(file_classes)
                                else:
                                    # If interrupted, try to cancel unfinished tasks
                                    if not future.done():
                                        future.cancel()
                            except Exception as e:
                                file_path = futures_map.get(future, "Unknown file")
                                self.logger.error(
                                    f"Error processing file {file_path}: {e}"
                                )

                except KeyboardInterrupt:
                    self.logger.info(
                        "Keyboard interrupt received, gracefully stopping..."
                    )
                    # Cancel all unfinished tasks
                    for future in futures_map:
                        if not future.done():
                            future.cancel()
                    # Set interrupt flag
                    if "cpp_inheritance_analyzer" in sys.modules:
                        sys.modules["cpp_inheritance_analyzer"].interrupted = True

                finally:
                    # Ensure progress bar is properly closed
                    pbar.close()

        # Save processed data even in case of interruption
        processed_count = len(all_classes)
        if interrupted or getattr(
            sys.modules.get("cpp_inheritance_analyzer", None), "interrupted", False
        ):
            self.logger.info(
                f"Analysis was interrupted, but successfully extracted {processed_count} class definitions"
            )
        else:
            self.logger.info(f"Extracted {processed_count} class definitions")

        return all_classes

    def _parse_file_with_progress(self, file_path: str, pbar) -> list[ClassInfo]:
        """
        Parse a file and update the progress bar with detailed status.

        Args:
            file_path: Path to the file to parse
            pbar: tqdm progress bar instance

        Returns:
            List of ClassInfo objects extracted from the file
        """
        import time

        start_time = time.time()

        try:
            # Check if file exists and is readable before attempting to parse
            if not os.path.isfile(file_path) or not os.access(file_path, os.R_OK):
                self.logger.warning(
                    f"File {file_path} does not exist or is not readable"
                )
                pbar.update(1)
                return []

            # Check file size - skip extremely large files
            file_size = os.path.getsize(file_path)
            if file_size > 10 * 1024 * 1024:  # 10MB
                self.logger.warning(
                    f"Skipping large file {file_path} ({file_size / 1024 / 1024:.2f} MB)"
                )
                pbar.update(1)
                return []

            # Parse the file
            result = self.parse_file(file_path)

            # Update progress with file info
            elapsed = time.time() - start_time
            if elapsed > 1.0:  # Log slow files
                self.logger.debug(
                    f"Parsed {file_path} in {elapsed:.2f}s ({len(result)} classes)"
                )

            pbar.set_postfix(classes=len(result), refresh=False)
            pbar.update(1)
            return result

        except Exception as e:
            self.logger.error(f"Error parsing {file_path}: {e}")
            pbar.update(1)
            return []
