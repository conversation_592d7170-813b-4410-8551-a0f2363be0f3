#!/usr/bin/env python3
"""
Regular expression-based C++ parser implementation.
"""

import os
import re
from concurrent.futures import ThreadPoolExecutor

from tqdm import tqdm

from cpp_inheritance_analyzer.models.class_info import ClassInfo
from cpp_inheritance_analyzer.parsers.base_parser import BaseParser


class CppParser(BaseParser):
    """High-performance C++ source code parser using regex for extracting class information."""

    def __init__(self, use_cache: bool = True, cache_dir: str | None = None):
        super().__init__(use_cache=use_cache, cache_dir=cache_dir)

        # Optimized regex patterns
        self.class_pattern = re.compile(
            r"(?:^|\s)(class|struct)\s+([A-Za-z_][A-Za-z0-9_:]*)"
            r"(?:\s*<[^>]*>)?\s*(?::\s*([^{]+?))?\s*\{",
            re.MULTILINE | re.DOTALL,
        )

        self.namespace_pattern = re.compile(
            r"namespace\s+([A-Za-z_][A-Za-z0-9_:]*)\s*\{", re.MULTILINE
        )

        self.template_pattern = re.compile(
            r"template\s*<([^>]+)>\s*(?:class|struct)", re.MULTILINE
        )

    def preprocess_code(self, content: str) -> str:
        """Remove comments and unnecessary whitespace for faster parsing."""
        # Remove single-line comments
        content = re.sub(r"//.*$", "", content, flags=re.MULTILINE)

        # Remove multi-line comments
        content = re.sub(r"/\*.*?\*/", "", content, flags=re.DOTALL)

        # Remove preprocessor directives (except namespace-related ones)
        content = re.sub(r"^\s*#(?!.*namespace).*$", "", content, flags=re.MULTILINE)

        return content

    def extract_inheritance_info(self, inheritance_str: str) -> list[tuple[str, str]]:
        """Extract base classes and access specifiers from inheritance string."""
        if not inheritance_str:
            return []

        # Split by comma, handling template parameters
        bases = []
        current = ""
        paren_depth = 0

        for char in inheritance_str:
            if char in "<(":
                paren_depth += 1
            elif char in ">)":
                paren_depth -= 1
            elif char == "," and paren_depth == 0:
                bases.append(current.strip())
                current = ""
                continue
            current += char

        if current.strip():
            bases.append(current.strip())

        # Parse each base class
        result = []
        for base in bases:
            base = base.strip()
            access = "private"  # default for class

            # Check for access specifier
            for access_type in ["public", "protected", "private"]:
                if base.startswith(access_type):
                    access = access_type
                    base = base[len(access_type) :].strip()
                    break

            # Remove virtual keyword
            base = re.sub(r"^virtual\s+", "", base)

            # Extract class name (remove template parameters for now)
            class_name = re.sub(r"<.*>", "", base).strip()

            if class_name:
                result.append((class_name, access))

        return result

    def parse_file(self, file_path: str) -> list[ClassInfo]:
        """Parse a single C++ file and extract class information."""
        # Check in-memory cache first
        if file_path in self.file_cache:
            return self.file_cache[file_path]

        # Try to load from persistent cache
        cache_hit, cached_classes = self._load_from_cache(file_path)
        if cache_hit:
            self.file_cache[file_path] = cached_classes
            return cached_classes

        try:
            with open(file_path, encoding="utf-8", errors="ignore") as f:
                content = f.read()
        except Exception as e:
            self.logger.warning(f"Failed to read {file_path}: {e}")
            return []

        # Preprocess to remove comments
        content = self.preprocess_code(content)

        classes = []

        # Find all class/struct definitions
        for match in self.class_pattern.finditer(content):
            keyword, class_name, inheritance = match.groups()

            # Find line number
            line_num = content[: match.start()].count("\n") + 1

            # Determine current namespace at this position
            pos = match.start()
            temp_namespace = ""
            for ns_match in self.namespace_pattern.finditer(content[:pos]):
                # Check if this namespace is still open
                ns_start = ns_match.end()
                brace_count = 1
                i = ns_start
                while i < pos and brace_count > 0:
                    if content[i] == "{":
                        brace_count += 1
                    elif content[i] == "}":
                        brace_count -= 1
                    i += 1

                if brace_count > 0:  # Namespace is still open
                    if temp_namespace:
                        temp_namespace += "::" + ns_match.group(1)
                    else:
                        temp_namespace = ns_match.group(1)

            # Parse inheritance
            base_classes = []
            access_specs = []
            if inheritance:
                inheritance_info = self.extract_inheritance_info(inheritance)
                for base_class, access in inheritance_info:
                    # Add namespace to base class if it doesn't have one and current class has namespace
                    if temp_namespace and "::" not in base_class:
                        base_class = f"{temp_namespace}::{base_class}"
                    base_classes.append(base_class)
                    access_specs.append(access)

            # Check for template
            template_params = []
            template_match = self.template_pattern.search(
                content[: match.start()][::-1]
            )
            if template_match:
                # This is a simplified template parameter extraction
                template_params = ["T"]  # Placeholder

            class_info = ClassInfo(
                name=class_name,
                file_path=file_path,
                line_number=line_num,
                base_classes=base_classes,
                access_specifiers=access_specs,
                is_struct=(keyword == "struct"),
                namespace=temp_namespace,
                template_params=template_params,
            )

            classes.append(class_info)

        # Save to in-memory cache
        self.file_cache[file_path] = classes

        # Save to persistent cache
        self._save_to_cache(file_path, classes)

        return classes

    def parse_directory(
        self, directory: str, extensions: set[str] = None, max_workers: int = None
    ) -> list[ClassInfo]:
        """Parse all C++ files in a directory tree."""
        if extensions is None:
            extensions = {".h", ".hpp", ".hxx", ".cpp", ".cc", ".cxx", ".c++"}

        # Find all relevant files
        cpp_files = []
        for root, dirs, files in os.walk(directory):
            # Skip common build directories
            dirs[:] = [
                d
                for d in dirs
                if not d.startswith(".")
                and d
                not in {
                    "build",
                    "cmake-build-debug",
                    "cmake-build-release",
                    "__pycache__",
                    "node_modules",
                }
            ]

            for file in files:
                if any(file.endswith(ext) for ext in extensions):
                    cpp_files.append(os.path.join(root, file))

        self.logger.info(f"Found {len(cpp_files)} C++ files to parse")

        # Parse files in parallel
        all_classes = []
        if max_workers is None:
            max_workers = min(32, os.cpu_count() + 4)

        # Check for interrupt flag
        import sys

        interrupted = False
        if "cpp_inheritance_analyzer" in sys.modules:
            # Get interrupt flag from main module
            interrupted = getattr(
                sys.modules["cpp_inheritance_analyzer"], "interrupted", False
            )

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Create a progress bar for the parsing process
            with tqdm(
                total=len(cpp_files),
                desc="Parsing C++ files",
                unit="file",
                ncols=100,
                colour="blue",
            ) as pbar:
                # Track submitted tasks
                futures_map = {}

                try:
                    # Submit tasks in batches to respond to interrupts promptly
                    batch_size = 50  # Number of files to process per batch

                    for i in range(0, len(cpp_files), batch_size):
                        # Check if interrupted
                        if interrupted or getattr(
                            sys.modules.get("cpp_inheritance_analyzer", None),
                            "interrupted",
                            False,
                        ):
                            self.logger.info(
                                "Interrupt signal detected, stopping submission of new tasks"
                            )
                            break

                        batch = cpp_files[i : i + batch_size]
                        batch_futures = []

                        # Submit tasks for this batch
                        for file_path in batch:
                            future = executor.submit(
                                self._parse_file_with_progress, file_path, pbar
                            )
                            futures_map[future] = file_path
                            batch_futures.append(future)

                        # Collect results for this batch
                        for future in batch_futures:
                            try:
                                if not interrupted and not getattr(
                                    sys.modules.get("cpp_inheritance_analyzer", None),
                                    "interrupted",
                                    False,
                                ):
                                    file_classes = future.result()
                                    all_classes.extend(file_classes)
                                else:
                                    # If interrupted, try to cancel unfinished tasks
                                    if not future.done():
                                        future.cancel()
                            except Exception as e:
                                file_path = futures_map.get(future, "Unknown file")
                                self.logger.error(
                                    f"Error processing file {file_path}: {e}"
                                )

                except KeyboardInterrupt:
                    self.logger.info(
                        "Keyboard interrupt received, gracefully stopping..."
                    )
                    # Cancel all unfinished tasks
                    for future in futures_map:
                        if not future.done():
                            future.cancel()
                    # Set interrupt flag
                    if "cpp_inheritance_analyzer" in sys.modules:
                        sys.modules["cpp_inheritance_analyzer"].interrupted = True

                finally:
                    # Ensure progress bar is properly closed
                    pbar.close()

        # Save processed data even in case of interruption
        processed_count = len(all_classes)
        if interrupted or getattr(
            sys.modules.get("cpp_inheritance_analyzer", None), "interrupted", False
        ):
            self.logger.info(
                f"Analysis was interrupted, but successfully extracted {processed_count} class definitions"
            )
        else:
            self.logger.info(f"Extracted {processed_count} class definitions")

        return all_classes

    def _parse_file_with_progress(self, file_path: str, pbar) -> list[ClassInfo]:
        """Parse a file and update the progress bar."""
        result = self.parse_file(file_path)
        pbar.update(1)
        return result
