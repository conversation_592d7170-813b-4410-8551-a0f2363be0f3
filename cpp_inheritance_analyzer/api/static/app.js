// Global variables
const API_BASE_URL = 'http://localhost:5000/api';
let cyGraph = null;
let currentClassData = null;
let progressInterval = null;

// DOM elements
const elements = {
    // Monitoring related
    directoryPath: document.getElementById('directoryPath'),
    startWatcherBtn: document.getElementById('startWatcherBtn'),
    stopWatcherBtn: document.getElementById('stopWatcherBtn'),
    watcherStatus: document.getElementById('watcherStatus'),

    // Indexing related
    indexDirectoryPath: document.getElementById('indexDirectoryPath'),
    indexCodebaseBtn: document.getElementById('indexCodebaseBtn'),
    indexStatus: document.getElementById('indexStatus'),
    indexProgressContainer: document.getElementById('indexProgressContainer'),
    indexProgressBar: document.getElementById('indexProgressBar'),
    indexProgressText: document.getElementById('indexProgressText'),
    indexProgressPercent: document.getElementById('indexProgressPercent'),
    indexProgressDetails: document.getElementById('indexProgressDetails'),

    // Search related
    classSearch: document.getElementById('classSearch'),
    searchBtn: document.getElementById('searchBtn'),
    classSelect: document.getElementById('classSelect'),

    // Graph related
    graphContainer: document.getElementById('graphContainer'),
    graphTitle: document.getElementById('graphTitle'),
    zoomInBtn: document.getElementById('zoomInBtn'),
    zoomOutBtn: document.getElementById('zoomOutBtn'),
    resetViewBtn: document.getElementById('resetViewBtn'),

    // Class details
    classDetails: document.getElementById('classDetails'),

    // Loading overlay
    loadingOverlay: document.getElementById('loadingOverlay'),
    loadingText: document.getElementById('loadingText')
};

// Initialization function
function init() {
    // Initialize graph
    initGraph();

    // Bind events
    bindEvents();

    // Get initial status
    getStatus();

    // Get indexed classes
    getClasses();
}

// Initialize graph
function initGraph() {
    // Register the dagre layout extension
    cytoscape.use(cytoscapeDagre);

    cyGraph = cytoscape({
        container: elements.graphContainer,
        style: [
            {
                selector: 'node',
                style: {
                    'label': 'data(label)',
                    'text-valign': 'center',
                    'text-halign': 'center',
                    'background-color': '#b8daff',
                    'border-width': 2,
                    'border-color': '#4a86e8',
                    'width': 'label',
                    'height': 'label',
                    'padding': '10px',
                    'text-wrap': 'wrap',
                    'text-max-width': '100px'
                }
            },
            {
                selector: 'node.target',
                style: {
                    'background-color': '#f8d7da',
                    'border-color': '#dc3545'
                }
            },
            {
                selector: 'node.struct',
                style: {
                    'background-color': '#d4edda',
                    'border-color': '#28a745'
                }
            },
            {
                selector: 'edge',
                style: {
                    'width': 2,
                    'line-color': '#999',
                    'target-arrow-color': '#999',
                    'target-arrow-shape': 'triangle',
                    'curve-style': 'bezier'
                }
            }
        ],
        layout: {
            name: 'dagre',
            rankDir: 'BT', // Bottom to top layout
            padding: 50,
            spacingFactor: 1.5
        },
        wheelSensitivity: 0.3
    });

    // Click node to show class details
    cyGraph.on('tap', 'node', function(evt) {
        const node = evt.target;
        const classId = node.id();
        showClassDetails(classId);
    });
}

// Bind events
function bindEvents() {
    // Start monitoring button
    elements.startWatcherBtn.addEventListener('click', startWatcher);

    // Stop monitoring button
    elements.stopWatcherBtn.addEventListener('click', stopWatcher);

    // Index codebase button
    elements.indexCodebaseBtn.addEventListener('click', indexCodebase);

    // Search button
    elements.searchBtn.addEventListener('click', () => {
        const className = elements.classSearch.value.trim();
        if (className) {
            searchClass(className);
        }
    });

    // Enter key search
    elements.classSearch.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            const className = elements.classSearch.value.trim();
            if (className) {
                searchClass(className);
            }
        }
    });

    // Class select dropdown
    elements.classSelect.addEventListener('change', () => {
        const selectedClass = elements.classSelect.value;
        if (selectedClass) {
            elements.classSearch.value = selectedClass;
            searchClass(selectedClass);
        }
    });

    // Zoom buttons
    elements.zoomInBtn.addEventListener('click', () => {
        cyGraph.zoom({
            level: cyGraph.zoom() * 1.2,
            renderedPosition: { x: elements.graphContainer.clientWidth / 2, y: elements.graphContainer.clientHeight / 2 }
        });
    });

    elements.zoomOutBtn.addEventListener('click', () => {
        cyGraph.zoom({
            level: cyGraph.zoom() * 0.8,
            renderedPosition: { x: elements.graphContainer.clientWidth / 2, y: elements.graphContainer.clientHeight / 2 }
        });
    });

    elements.resetViewBtn.addEventListener('click', () => {
        cyGraph.fit();
        cyGraph.center();
    });

    // Sync directory paths
    elements.directoryPath.addEventListener('input', () => {
        elements.indexDirectoryPath.value = elements.directoryPath.value;
    });

    elements.indexDirectoryPath.addEventListener('input', () => {
        elements.directoryPath.value = elements.indexDirectoryPath.value;
    });
}

// API call function
async function fetchAPI(endpoint, method = 'GET', data = null) {
    try {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        if (data && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(data);
        }

        console.log(`Calling API: ${API_BASE_URL}${endpoint}`, options);
        const response = await fetch(`${API_BASE_URL}${endpoint}`, options);
        const result = await response.json();
        console.log(`API response:`, result);
        return result;
    } catch (error) {
        console.error('API call error:', error);
        return { success: false, error: error.message };
    }
}

// Get status
async function getStatus() {
    showLoading('Getting status...');

    try {
        const result = await fetchAPI('/status');

        if (result.is_watching) {
            elements.watcherStatus.textContent = `Status: Monitoring ${result.monitored_directory}`;
            elements.watcherStatus.className = 'alert alert-success';
            elements.startWatcherBtn.disabled = true;
            elements.stopWatcherBtn.disabled = false;
            elements.directoryPath.value = result.monitored_directory;
            elements.indexDirectoryPath.value = result.monitored_directory;
        } else {
            elements.watcherStatus.textContent = 'Status: Not monitoring';
            elements.watcherStatus.className = 'alert alert-secondary';
            elements.startWatcherBtn.disabled = false;
            elements.stopWatcherBtn.disabled = true;
        }

        if (result.class_count > 0) {
            elements.indexStatus.textContent = `Indexed ${result.class_count} classes`;
            elements.indexStatus.className = 'alert alert-success';
        }
    } catch (error) {
        console.error('Failed to get status:', error);
    } finally {
        hideLoading();
    }
}

// Start monitoring
async function startWatcher() {
    const directory = elements.directoryPath.value.trim();

    if (!directory) {
        alert('Please enter a directory path to monitor');
        return;
    }

    showLoading('Starting monitoring...');

    try {
        const result = await fetchAPI('/watcher/start', 'POST', {
            directory,
            extensions: ['.h', '.hpp', '.hxx', '.cpp', '.cc', '.cxx'],
            parser_type: 'regex'
        });

        if (result.success) {
            elements.watcherStatus.textContent = `Status: Monitoring ${directory}`;
            elements.watcherStatus.className = 'alert alert-success';
            elements.startWatcherBtn.disabled = true;
            elements.stopWatcherBtn.disabled = false;
        } else {
            alert(`Failed to start monitoring: ${result.error || 'Unknown error'}`);
        }
    } catch (error) {
        console.error('Failed to start monitoring:', error);
        alert(`Failed to start monitoring: ${error.message}`);
    } finally {
        hideLoading();
    }
}

// Stop monitoring
async function stopWatcher() {
    showLoading('Stopping monitoring...');

    try {
        const result = await fetchAPI('/watcher/stop', 'POST');

        if (result.success) {
            elements.watcherStatus.textContent = 'Status: Not monitoring';
            elements.watcherStatus.className = 'alert alert-secondary';
            elements.startWatcherBtn.disabled = false;
            elements.stopWatcherBtn.disabled = true;
        } else {
            alert(`Failed to stop monitoring: ${result.error || 'Unknown error'}`);
        }
    } catch (error) {
        console.error('Failed to stop monitoring:', error);
        alert(`Failed to stop monitoring: ${error.message}`);
    } finally {
        hideLoading();
    }
}

// Index codebase
async function indexCodebase() {
    const directory = elements.indexDirectoryPath.value.trim();

    if (!directory) {
        alert('Please enter a directory path to index');
        return;
    }

    // Disable the button during indexing
    elements.indexCodebaseBtn.disabled = true;
    elements.indexCodebaseBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Indexing...';

    try {
        const result = await fetchAPI('/index', 'POST', {
            directory,
            extensions: ['.h', '.hpp', '.hxx', '.cpp', '.cc', '.cxx'],
            parser_type: 'regex'
        });

        if (result.success) {
            // Start progress tracking
            startProgressTracking();

            elements.indexStatus.textContent = 'Indexing in progress...';
            elements.indexStatus.className = 'alert alert-info';
        } else {
            alert(`Failed to start indexing: ${result.error || 'Unknown error'}`);
            elements.indexStatus.textContent = `Error: ${result.error || 'Unknown error'}`;
            elements.indexStatus.className = 'alert alert-danger';
        }
    } catch (error) {
        console.error('Failed to start indexing:', error);
        alert(`Failed to start indexing: ${error.message}`);
        elements.indexStatus.textContent = `Error: ${error.message}`;
        elements.indexStatus.className = 'alert alert-danger';
    } finally {
        // Re-enable the button
        elements.indexCodebaseBtn.disabled = false;
        elements.indexCodebaseBtn.innerHTML = '<i class="bi bi-database-fill"></i> Index Codebase';
    }
}

// Get class list
async function getClasses() {
    try {
        const result = await fetchAPI('/classes');

        if (result.success && result.classes) {
            // Clear dropdown
            elements.classSelect.innerHTML = '';

            // Add classes
            result.classes.sort().forEach(className => {
                const option = document.createElement('option');
                option.value = className;
                option.textContent = className;
                elements.classSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Failed to get class list:', error);
    }
}

// Search class
async function searchClass(className) {
    showLoading(`Searching for class ${className}...`);

    try {
        const result = await fetchAPI(`/search?class_name=${encodeURIComponent(className)}`);

        if (result.success && result.data) {
            // Save current class data
            currentClassData = result.data;

            // Update graph title
            elements.graphTitle.textContent = `Inheritance Graph: ${className}`;

            // Render graph
            renderGraph(result.data);

            // Show target class details
            showClassDetails(result.data.target_class);
        } else {
            alert(`Failed to search class: ${result.error || 'Unknown error'}`);
        }
    } catch (error) {
        console.error('Failed to search class:', error);
        alert(`Failed to search class: ${error.message}`);
    } finally {
        hideLoading();
    }
}

// Render graph
function renderGraph(data) {
    // Clear graph
    cyGraph.elements().remove();

    const elements = [];
    const targetClass = data.target_class;

    // Add nodes
    Object.entries(data.classes).forEach(([id, classInfo]) => {
        elements.push({
            data: {
                id: id,
                label: classInfo.name
            },
            classes: [
                'node',
                id === targetClass ? 'target' : (classInfo.is_struct ? 'struct' : 'class')
            ]
        });
    });

    // Add edges
    data.inheritance_relationships.forEach(rel => {
        elements.push({
            data: {
                source: rel.child,
                target: rel.parent
            }
        });
    });

    // Add elements to graph
    cyGraph.add(elements);

    // Apply layout
    const layout = cyGraph.layout({
        name: 'dagre',
        rankDir: 'BT', // Bottom to top layout
        padding: 50,
        spacingFactor: 1.5,
        animate: false,
        fit: true
    });

    layout.run();

    // Center view
    cyGraph.fit();
    cyGraph.center();
}

// Show class details
function showClassDetails(classId) {
    if (!currentClassData || !currentClassData.classes[classId]) {
        return;
    }

    const classInfo = currentClassData.classes[classId];

    // Build details HTML
    let html = `
        <div class="class-detail-card">
            <h4>${classInfo.name}</h4>
            <p><span class="label">Type:</span> ${classInfo.is_struct ? 'Struct' : 'Class'}</p>
            <p><span class="label">Namespace:</span> ${classInfo.namespace || 'Global'}</p>
            <p><span class="label">File:</span> ${classInfo.file_path}</p>
            <p><span class="label">Line:</span> ${classInfo.line_number}</p>
    `;

    // Base classes
    if (classInfo.base_classes && classInfo.base_classes.length > 0) {
        html += `<p><span class="label">Base Classes:</span></p><ul>`;
        classInfo.base_classes.forEach((baseClass, index) => {
            const accessSpecifier = classInfo.access_specifiers[index] || 'public';
            html += `<li>${accessSpecifier} ${baseClass}</li>`;
        });
        html += `</ul>`;
    } else {
        html += `<p><span class="label">Base Classes:</span> None</p>`;
    }

    // Derived classes
    const derivedClasses = [];
    currentClassData.inheritance_relationships.forEach(rel => {
        if (rel.parent === classId) {
            derivedClasses.push(rel.child);
        }
    });

    if (derivedClasses.length > 0) {
        html += `<p><span class="label">Derived Classes:</span></p><ul>`;
        derivedClasses.forEach(derivedClass => {
            html += `<li>${derivedClass}</li>`;
        });
        html += `</ul>`;
    } else {
        html += `<p><span class="label">Derived Classes:</span> None</p>`;
    }

    html += `</div>`;

    // Update details area
    elements.classDetails.innerHTML = html;

    // Highlight selected node
    cyGraph.nodes().removeClass('selected');
    cyGraph.getElementById(classId).addClass('selected');
}

// Show loading overlay
function showLoading(text = 'Loading...') {
    elements.loadingText.textContent = text;
    elements.loadingOverlay.classList.remove('d-none');
}

// Hide loading overlay
function hideLoading() {
    elements.loadingOverlay.classList.add('d-none');
}

// Progress tracking functions
async function getProgress() {
    try {
        const result = await fetchAPI('/progress');
        return result;
    } catch (error) {
        console.error('Failed to get progress:', error);
        return null;
    }
}

function showProgressBar() {
    elements.indexProgressContainer.classList.remove('d-none');
}

function hideProgressBar() {
    elements.indexProgressContainer.classList.add('d-none');
}

function updateProgressBar(progress) {
    const percentage = Math.round(progress.progress_percentage || 0);

    // Update progress bar
    elements.indexProgressBar.style.width = `${percentage}%`;
    elements.indexProgressBar.setAttribute('aria-valuenow', percentage);

    // Update percentage text
    elements.indexProgressPercent.textContent = `${percentage}%`;

    // Update progress text
    elements.indexProgressText.textContent = progress.current_step || 'Processing...';

    // Update details
    let details = '';
    if (progress.files_processed && progress.total_files) {
        details += `Files: ${progress.files_processed}/${progress.total_files}`;
    }
    if (progress.classes_found) {
        details += details ? ` | Classes: ${progress.classes_found}` : `Classes: ${progress.classes_found}`;
    }
    if (progress.estimated_time_remaining && progress.estimated_time_remaining > 0) {
        const minutes = Math.floor(progress.estimated_time_remaining / 60);
        const seconds = Math.round(progress.estimated_time_remaining % 60);
        details += details ? ` | ETA: ${minutes}m ${seconds}s` : `ETA: ${minutes}m ${seconds}s`;
    }

    elements.indexProgressDetails.textContent = details || 'Preparing...';
}

function startProgressTracking() {
    if (progressInterval) {
        clearInterval(progressInterval);
    }

    showProgressBar();

    progressInterval = setInterval(async () => {
        const progress = await getProgress();
        if (progress) {
            updateProgressBar(progress);

            // Stop tracking if indexing is complete
            if (!progress.is_indexing) {
                stopProgressTracking();

                // Update status
                if (progress.classes_found > 0) {
                    elements.indexStatus.textContent = `Indexed ${progress.classes_found} classes`;
                    elements.indexStatus.className = 'alert alert-success';

                    // Update class list
                    getClasses();
                } else if (progress.current_step.includes('Error')) {
                    elements.indexStatus.textContent = progress.current_step;
                    elements.indexStatus.className = 'alert alert-danger';
                }
            }
        }
    }, 1000); // Update every second
}

function stopProgressTracking() {
    if (progressInterval) {
        clearInterval(progressInterval);
        progressInterval = null;
    }

    // Hide progress bar after a short delay to show completion
    setTimeout(() => {
        hideProgressBar();
    }, 2000);
}

// Initialize when page is loaded
document.addEventListener('DOMContentLoaded', init);
