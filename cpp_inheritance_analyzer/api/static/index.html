<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C++ Inheritance Analyzer</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="styles.css">
    <script src="https://cdn.jsdelivr.net/npm/cytoscape@3.26.0/dist/cytoscape.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/dagre@0.8.5/dist/dagre.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/cytoscape-dagre@2.5.0/cytoscape-dagre.min.js"></script>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 sidebar">
                <div class="sidebar-header">
                    <h3>C++ Inheritance Analyzer</h3>
                </div>

                <!-- Directory Monitoring Panel -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>Directory Monitoring</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="directoryPath" class="form-label">Code Directory Path</label>
                            <input type="text" class="form-control" id="directoryPath" placeholder="/path/to/cpp/code">
                        </div>
                        <div class="d-grid gap-2">
                            <button id="startWatcherBtn" class="btn btn-primary">
                                <i class="bi bi-play-fill"></i> Start Monitoring
                            </button>
                            <button id="stopWatcherBtn" class="btn btn-danger" disabled>
                                <i class="bi bi-stop-fill"></i> Stop Monitoring
                            </button>
                        </div>
                        <div class="mt-3">
                            <div id="watcherStatus" class="alert alert-secondary">
                                Status: Not monitoring
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Code Indexing Panel -->
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>Code Indexing</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="indexDirectoryPath" class="form-label">Index Directory Path</label>
                            <input type="text" class="form-control" id="indexDirectoryPath" placeholder="/path/to/cpp/code">
                        </div>
                        <div class="d-grid gap-2">
                            <button id="indexCodebaseBtn" class="btn btn-success">
                                <i class="bi bi-database-fill"></i> Index Codebase
                            </button>
                        </div>
                        <div class="mt-3">
                            <div id="indexStatus" class="alert alert-secondary">
                                Not indexed
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Class Search Panel -->
                <div class="card">
                    <div class="card-header">
                        <h5>Class Search</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="classSearch" class="form-label">Class Name</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="classSearch" placeholder="MyClass">
                                <button id="searchBtn" class="btn btn-primary">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="classSelect" class="form-label">Indexed Classes</label>
                            <select class="form-select" id="classSelect" size="10">
                                <option disabled>Please index the codebase first...</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main Content Area -->
            <div class="col-md-9 main-content">
                <!-- Graph Area -->
                <div class="card mb-3">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 id="graphTitle">Inheritance Graph</h5>
                        <div class="btn-group" role="group">
                            <button id="zoomInBtn" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-zoom-in"></i>
                            </button>
                            <button id="zoomOutBtn" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-zoom-out"></i>
                            </button>
                            <button id="resetViewBtn" class="btn btn-sm btn-outline-secondary">
                                <i class="bi bi-arrows-fullscreen"></i>
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="graphContainer"></div>
                    </div>
                </div>

                <!-- Class Details Area -->
                <div class="card">
                    <div class="card-header">
                        <h5>Class Details</h5>
                    </div>
                    <div class="card-body">
                        <div id="classDetails">
                            <div class="alert alert-info">
                                Please select a class to view details
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loadingOverlay" class="loading-overlay d-none">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div class="mt-2 text-light" id="loadingText">Loading...</div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>
</html>
