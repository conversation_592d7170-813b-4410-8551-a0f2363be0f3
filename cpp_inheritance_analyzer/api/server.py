#!/usr/bin/env python3
"""
Flask API server for C++ Inheritance Analyzer.
Provides endpoints for controlling the file watcher and retrieving inheritance data.
"""

import json
import logging
import os
import signal
import sys
import threading
import time
from pathlib import Path
from typing import Dict, List, Optional, Union

from flask import Flask, jsonify, request, send_from_directory
from flask_cors import CORS

from cpp_inheritance_analyzer.analyzer import CppInheritanceAnalyzer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(os.path.join("/tmp", "cpp_analyzer_api.log")),
    ],
)
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__, static_folder="static")
CORS(app)  # Enable CORS for all routes

# Global variables
watcher_thread = None
watcher_daemon = None
analyzer = None
monitored_directory = None
is_watching = False
pid_file = "/tmp/cpp_cache_watcher.pid"

# Progress tracking
indexing_progress = {
    "is_indexing": False,
    "current_step": "",
    "files_processed": 0,
    "total_files": 0,
    "classes_found": 0,
    "progress_percentage": 0,
    "start_time": None,
    "estimated_time_remaining": None
}


def create_analyzer(parser_type="regex", cache_dir=None) -> CppInheritanceAnalyzer:
    """Create a new analyzer instance."""
    global analyzer
    analyzer = CppInheritanceAnalyzer(
        log_level=logging.INFO,
        parser_type=parser_type,
        use_cache=True,
        cache_dir=cache_dir,
    )
    return analyzer


def start_watcher(
    directory: str, extensions=None, parser_type="regex", cache_dir=None
) -> bool:
    """Start the file watcher daemon in a separate thread."""
    global watcher_thread, watcher_daemon, is_watching, monitored_directory, analyzer

    if is_watching:
        logger.warning("Watcher is already running")
        return False

    try:
        # Validate directory
        if not os.path.isdir(directory):
            logger.error(f"Directory does not exist: {directory}")
            return False

        logger.info(f"Creating file watcher for directory: {directory}")

        # Convert extensions list to set if needed
        if extensions and isinstance(extensions, list):
            extensions = set(extensions)
        else:
            extensions = {".h", ".hpp", ".hxx", ".cpp", ".cc", ".cxx"}

        # Create analyzer if it doesn't exist
        if not analyzer:
            create_analyzer(parser_type, cache_dir)

        # Import here to avoid circular imports
        from watchdog.observers import Observer
        from cpp_inheritance_analyzer.utils.file_watcher import CppFileHandler

        # Create observer
        observer = Observer()

        # Create event handler
        event_handler = CppFileHandler(analyzer.parser, extensions)

        # Schedule directory monitoring
        observer.schedule(event_handler, directory, recursive=True)

        # Store the observer in the global watcher_daemon variable
        watcher_daemon = observer

        logger.info("Starting file watcher in a separate thread")

        # Define a function to run the observer
        def run_observer():
            try:
                observer.start()
                logger.info(f"File watcher started for directory: {directory}")
                while is_watching:
                    time.sleep(1)
            except Exception as e:
                logger.error(f"Error in file watcher thread: {e}")
            finally:
                if observer.is_alive():
                    observer.stop()
                    observer.join()
                logger.info("File watcher thread stopped")

        # Start watcher in a separate thread
        watcher_thread = threading.Thread(target=run_observer)
        watcher_thread.daemon = True
        watcher_thread.start()

        # Update global state
        is_watching = True
        monitored_directory = directory

        logger.info(f"Started file watcher for directory: {directory}")
        return True

    except Exception as e:
        import traceback

        logger.error(f"Failed to start watcher: {e}")
        logger.error(traceback.format_exc())
        return False


def stop_watcher() -> bool:
    """Stop the file watcher daemon."""
    global watcher_daemon, watcher_thread, is_watching

    if not is_watching or not watcher_daemon:
        logger.warning("No watcher is running")
        return False

    try:
        # First set is_watching to False to signal the thread to stop
        is_watching = False

        # Give the thread a moment to notice the flag change
        time.sleep(0.5)

        # Stop the observer if it's still running
        if hasattr(watcher_daemon, 'stop') and callable(watcher_daemon.stop):
            if hasattr(watcher_daemon, 'is_alive') and watcher_daemon.is_alive():
                watcher_daemon.stop()
                watcher_daemon.join()

        # Reset variables
        watcher_thread = None
        watcher_daemon = None

        logger.info("Stopped file watcher")
        return True

    except Exception as e:
        import traceback
        logger.error(f"Failed to stop watcher: {e}")
        logger.error(traceback.format_exc())
        return False


@app.route("/api/status", methods=["GET"])
def get_status():
    """Get the status of the file watcher and analyzer."""
    global is_watching, monitored_directory, analyzer

    status = {
        "is_watching": is_watching,
        "monitored_directory": monitored_directory,
        "has_analyzer": analyzer is not None,
        "cache_dir": analyzer.cache_dir if analyzer else None,
        "parser_type": analyzer.parser.parser_type
        if analyzer and hasattr(analyzer.parser, "parser_type")
        else None,
        "class_count": len(analyzer.graph.classes)
        if analyzer and analyzer.graph
        else 0,
    }

    return jsonify(status)


@app.route("/api/progress", methods=["GET"])
def get_progress():
    """Get the current indexing progress."""
    global indexing_progress

    # Calculate estimated time remaining if we have enough data
    if (indexing_progress["is_indexing"] and
        indexing_progress["start_time"] and
        indexing_progress["files_processed"] > 0 and
        indexing_progress["total_files"] > 0):

        elapsed_time = time.time() - indexing_progress["start_time"]
        files_remaining = indexing_progress["total_files"] - indexing_progress["files_processed"]

        if indexing_progress["files_processed"] > 0:
            avg_time_per_file = elapsed_time / indexing_progress["files_processed"]
            estimated_remaining = avg_time_per_file * files_remaining
            indexing_progress["estimated_time_remaining"] = estimated_remaining

    return jsonify(indexing_progress)


@app.route("/api/watcher/start", methods=["POST"])
def api_start_watcher():
    """Start the file watcher daemon."""
    try:
        logger.info("Received request to start watcher")

        # Get request data
        data = request.json
        if not data:
            logger.error("No JSON data in request")
            return jsonify({"success": False, "error": "No JSON data provided"}), 400

        logger.info(f"Request data: {data}")

        directory = data.get("directory")
        extensions = data.get(
            "extensions", [".h", ".hpp", ".hxx", ".cpp", ".cc", ".cxx"]
        )
        parser_type = data.get("parser_type", "regex")
        cache_dir = data.get("cache_dir")

        if not directory:
            logger.error("No directory provided in request")
            return jsonify({"success": False, "error": "Directory is required"}), 400

        # Create analyzer if it doesn't exist
        if not analyzer:
            logger.info(
                f"Creating analyzer with parser_type={parser_type}, cache_dir={cache_dir}"
            )
            create_analyzer(parser_type, cache_dir)

        # Start watcher
        logger.info(f"Starting watcher for directory: {directory}")
        success = start_watcher(directory, extensions, parser_type, cache_dir)

        if success:
            logger.info("Watcher started successfully")
            return jsonify({"success": True})
        else:
            logger.error("Failed to start watcher")
            return jsonify(
                {
                    "success": False,
                    "error": "Failed to start watcher. Check server logs for details.",
                }
            )

    except Exception as e:
        import traceback

        logger.error(f"Error in api_start_watcher: {e}")
        logger.error(traceback.format_exc())
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/watcher/stop", methods=["POST"])
def api_stop_watcher():
    """Stop the file watcher daemon."""
    success = stop_watcher()
    return jsonify({"success": success})


@app.route("/api/search", methods=["GET"])
def search_class():
    """Search for a class and return its inheritance graph."""
    global analyzer

    class_name = request.args.get("class_name")
    if not class_name:
        return jsonify({"success": False, "error": "Class name is required"}), 400

    # Create analyzer if it doesn't exist
    if not analyzer:
        create_analyzer()

    # Load from cache if graph is empty
    if not analyzer.graph.classes:
        if not analyzer.load_from_cache():
            return jsonify(
                {
                    "success": False,
                    "error": "Failed to load class information from cache. Try indexing the codebase first.",
                }
            ), 400

    try:
        # Search for the class
        family_graph = analyzer.search_class(class_name, "/tmp", "json")

        if not family_graph:
            return jsonify(
                {"success": False, "error": f"Class '{class_name}' not found"}
            ), 404

        # Read the generated JSON file
        output_base = os.path.join("/tmp", f"{class_name.split('::')[-1]}_inheritance")
        json_file = f"{output_base}.json"

        if not os.path.exists(json_file):
            return jsonify(
                {"success": False, "error": "Failed to generate inheritance graph"}
            ), 500

        with open(json_file, "r") as f:
            graph_data = json.load(f)

        return jsonify({"success": True, "data": graph_data})

    except Exception as e:
        logger.error(f"Error searching for class: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/classes", methods=["GET"])
def get_classes():
    """Get a list of all classes in the cache."""
    global analyzer

    # Create analyzer if it doesn't exist
    if not analyzer:
        create_analyzer()

    # Load from cache if graph is empty
    if not analyzer.graph.classes:
        if not analyzer.load_from_cache():
            return jsonify(
                {
                    "success": False,
                    "error": "Failed to load class information from cache. Try indexing the codebase first.",
                }
            ), 400

    classes = list(analyzer.graph.classes.keys())
    return jsonify({"success": True, "classes": classes})


def update_progress(step, files_processed=None, total_files=None, classes_found=None):
    """Update the indexing progress."""
    global indexing_progress

    indexing_progress["current_step"] = step

    if files_processed is not None:
        indexing_progress["files_processed"] = files_processed

    if total_files is not None:
        indexing_progress["total_files"] = total_files

    if classes_found is not None:
        indexing_progress["classes_found"] = classes_found

    # Calculate progress percentage
    if indexing_progress["total_files"] > 0:
        indexing_progress["progress_percentage"] = (
            indexing_progress["files_processed"] / indexing_progress["total_files"] * 100
        )


def index_codebase_async(directory, extensions, parser_type, cache_dir):
    """Asynchronously index the codebase with progress tracking."""
    global analyzer, indexing_progress

    try:
        # Reset progress
        indexing_progress.update({
            "is_indexing": True,
            "current_step": "Starting indexing...",
            "files_processed": 0,
            "total_files": 0,
            "classes_found": 0,
            "progress_percentage": 0,
            "start_time": time.time(),
            "estimated_time_remaining": None
        })

        # Create analyzer
        analyzer = create_analyzer(parser_type, cache_dir)

        # Index the codebase
        logger.info(f"Indexing codebase in: {directory}")
        update_progress("Analyzing codebase...")

        graph = analyzer.analyze_codebase(directory, set(extensions))

        # Update final progress
        indexing_progress.update({
            "is_indexing": False,
            "current_step": f"Completed! Indexed {len(graph.classes)} classes",
            "classes_found": len(graph.classes),
            "progress_percentage": 100,
            "estimated_time_remaining": 0
        })

        logger.info(f"Indexing completed: {len(graph.classes)} classes found")

    except Exception as e:
        logger.error(f"Error indexing codebase: {e}")
        indexing_progress.update({
            "is_indexing": False,
            "current_step": f"Error: {str(e)}",
            "progress_percentage": 0,
            "estimated_time_remaining": None
        })


@app.route("/api/index", methods=["POST"])
def index_codebase():
    """Index a codebase and generate cache for all classes."""
    global indexing_progress

    data = request.json
    directory = data.get("directory")
    extensions = data.get("extensions", [".h", ".hpp", ".hxx", ".cpp", ".cc", ".cxx"])
    parser_type = data.get("parser_type", "regex")
    cache_dir = data.get("cache_dir")

    if not directory:
        return jsonify({"success": False, "error": "Directory is required"}), 400

    # Check if already indexing
    if indexing_progress["is_indexing"]:
        return jsonify({"success": False, "error": "Indexing is already in progress"}), 400

    try:
        # Start indexing in a separate thread
        indexing_thread = threading.Thread(
            target=index_codebase_async,
            args=(directory, extensions, parser_type, cache_dir)
        )
        indexing_thread.daemon = True
        indexing_thread.start()

        return jsonify({
            "success": True,
            "message": "Indexing started. Use /api/progress to check status."
        })

    except Exception as e:
        logger.error(f"Error starting indexing: {e}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/", defaults={"path": "index.html"})
@app.route("/<path:path>")
def serve_static(path):
    """Serve static files."""
    return send_from_directory(app.static_folder, path)


def main():
    """Run the Flask API server."""
    # Create analyzer
    create_analyzer()

    # Run Flask app
    app.run(host="0.0.0.0", port=5000, debug=True)


# This ensures the module is executed correctly whether run as a module or imported
if __name__ == "__main__" or __name__ == "cpp_inheritance_analyzer.api.server":
    main()
